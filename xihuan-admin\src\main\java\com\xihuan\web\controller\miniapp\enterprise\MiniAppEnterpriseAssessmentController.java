package com.xihuan.web.controller.miniapp.enterprise;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTScale;
import com.xihuan.common.core.domain.entity.PsyTEnterprise;
import com.xihuan.common.core.domain.entity.PsyTEnterpriseAssessmentPlan;
import com.xihuan.common.core.domain.entity.PsyTEnterpriseAssessmentParticipant;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 小程序企业端测评Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/enterprise/assessment")
public class MiniAppEnterpriseAssessmentController extends BaseController {
    
    @Autowired
    private IPsyTScaleService scaleService;
    
    @Autowired
    private IPsyTEnterpriseService enterpriseService;
    
    @Autowired
    private IPsyTEnterpriseAssessmentPlanService planService;
    
    @Autowired
    private IPsyTEnterpriseAssessmentParticipantService participantService;
    
    @Autowired
    private IPsyTAssessmentService assessmentService;

    /**
     * 查询企业可用量表
     */
    @GetMapping("/scale/available")
    public TableDataInfo getAvailableScales(@RequestParam Long enterpriseId) {
        startPage();
        List<PsyTScale> list = scaleService.selectAvailableScalesForEnterprise(enterpriseId);
        return getDataTable(list);
    }

    /**
     * 查询企业信息
     */
    @GetMapping("/enterprise/{enterpriseId}")
    public AjaxResult getEnterpriseInfo(@PathVariable("enterpriseId") Long enterpriseId) {
        PsyTEnterprise enterprise = enterpriseService.selectEnterpriseWithDetails(enterpriseId);
        if (enterprise == null) {
            return error("企业信息不存在");
        }
        return success(enterprise);
    }

    /**
     * 查询企业测评计划列表
     */
    @GetMapping("/plan/list")
    public TableDataInfo getPlanList(@RequestParam Long enterpriseId) {
        startPage();
        List<PsyTEnterpriseAssessmentPlan> list = planService.selectPlansByEnterpriseId(enterpriseId);
        return getDataTable(list);
    }

    /**
     * 查询进行中的测评计划
     */
    @GetMapping("/plan/in-progress")
    public AjaxResult getInProgressPlans(@RequestParam Long enterpriseId) {
        List<PsyTEnterpriseAssessmentPlan> list = planService.selectInProgressPlans(enterpriseId);
        return success(list);
    }

    /**
     * 查询已结束的测评计划
     */
    @GetMapping("/plan/ended")
    public TableDataInfo getEndedPlans(@RequestParam Long enterpriseId) {
        startPage();
        List<PsyTEnterpriseAssessmentPlan> list = planService.selectEndedPlans(enterpriseId);
        return getDataTable(list);
    }

    /**
     * 获取测评计划详情
     */
    @GetMapping("/plan/{planId}")
    public AjaxResult getPlanDetail(@PathVariable("planId") Long planId) {
        PsyTEnterpriseAssessmentPlan plan = planService.selectPlanWithDetails(planId);
        if (plan == null) {
                       return error("测评计划不存在");
        }
        return success(plan);
    }

    /**
     * 创建测评计划
     */
    @Log(title = "企业测评计划", businessType = BusinessType.INSERT)
    @PostMapping("/plan/create")
    public AjaxResult createPlan(@Valid @RequestBody PsyTEnterpriseAssessmentPlan plan) {
        return toAjax(planService.createAssessmentPlan(plan));
    }

    /**
     * 启动测评计划
     */
    @Log(title = "企业测评计划", businessType = BusinessType.UPDATE)
    @PostMapping("/plan/{planId}/start")
    public AjaxResult startPlan(@PathVariable("planId") Long planId) {
        return toAjax(planService.startAssessmentPlan(planId));
    }

    /**
     * 暂停测评计划
     */
    @Log(title = "企业测评计划", businessType = BusinessType.UPDATE)
    @PostMapping("/plan/{planId}/pause")
    public AjaxResult pausePlan(@PathVariable("planId") Long planId) {
        return toAjax(planService.pauseAssessmentPlan(planId));
    }

    /**
     * 恢复测评计划
     */
    @Log(title = "企业测评计划", businessType = BusinessType.UPDATE)
    @PostMapping("/plan/{planId}/resume")
    public AjaxResult resumePlan(@PathVariable("planId") Long planId) {
        return toAjax(planService.resumeAssessmentPlan(planId));
    }

    /**
     * 结束测评计划
     */
    @Log(title = "企业测评计划", businessType = BusinessType.UPDATE)
    @PostMapping("/plan/{planId}/end")
    public AjaxResult endPlan(@PathVariable("planId") Long planId) {
        return toAjax(planService.endAssessmentPlan(planId));
    }

    /**
     * 取消测评计划
     */
    @Log(title = "企业测评计划", businessType = BusinessType.UPDATE)
    @PostMapping("/plan/{planId}/cancel")
    public AjaxResult cancelPlan(@PathVariable("planId") Long planId, @RequestParam String reason) {
        return toAjax(planService.cancelAssessmentPlan(planId, reason));
    }

    /**
     * 查询计划参与者列表
     */
    @GetMapping("/plan/{planId}/participants")
    public TableDataInfo getPlanParticipants(@PathVariable("planId") Long planId) {
        startPage();
        List<PsyTEnterpriseAssessmentParticipant> list = participantService.selectParticipantsByPlanId(planId);
        return getDataTable(list);
    }

    /**
     * 查询计划未开始参与者
     */
    @GetMapping("/plan/{planId}/participants/not-started")
    public AjaxResult getNotStartedParticipants(@PathVariable("planId") Long planId) {
        List<PsyTEnterpriseAssessmentParticipant> list = participantService.selectNotStartedParticipants(planId);
        return success(list);
    }

    /**
     * 查询计划进行中参与者
     */
    @GetMapping("/plan/{planId}/participants/in-progress")
    public AjaxResult getInProgressParticipants(@PathVariable("planId") Long planId) {
        List<PsyTEnterpriseAssessmentParticipant> list = participantService.selectInProgressParticipants(planId);
        return success(list);
    }

    /**
     * 查询计划已完成参与者
     */
    @GetMapping("/plan/{planId}/participants/completed")
    public AjaxResult getCompletedParticipants(@PathVariable("planId") Long planId) {
        List<PsyTEnterpriseAssessmentParticipant> list = participantService.selectCompletedParticipants(planId);
        return success(list);
    }

    /**
     * 查询计划已放弃参与者
     */
    @GetMapping("/plan/{planId}/participants/abandoned")
    public AjaxResult getAbandonedParticipants(@PathVariable("planId") Long planId) {
        List<PsyTEnterpriseAssessmentParticipant> list = participantService.selectAbandonedParticipants(planId);
        return success(list);
    }

    /**
     * 添加参与者
     */
    @Log(title = "企业测评参与者", businessType = BusinessType.INSERT)
    @PostMapping("/plan/{planId}/participants/add")
    public AjaxResult addParticipants(
            @PathVariable("planId") Long planId,
            @RequestBody List<PsyTEnterpriseAssessmentParticipant> participants) {
        for (PsyTEnterpriseAssessmentParticipant participant : participants) {
            participant.setPlanId(planId);
        }
        return toAjax(participantService.batchInsertParticipants(participants));
    }

    /**
     * 移除参与者
     */
    @Log(title = "Enterprise Assessment Participant", businessType = BusinessType.DELETE)
    @DeleteMapping("/plan/{planId}/participants/{participantIds}")
    public AjaxResult removeParticipants(
            @PathVariable("planId") Long planId,
            @PathVariable Long[] participantIds) {
        return toAjax(participantService.deleteParticipantByIds(participantIds));
    }

    /**
     * 发送测评提醒
     */
    @Log(title = "企业测评计划", businessType = BusinessType.OTHER)
    @PostMapping("/plan/{planId}/reminder")
    public AjaxResult sendReminder(@PathVariable("planId") Long planId) {
        return toAjax(planService.sendPlanReminder(planId));
    }

    /**
     * 查询计划统计信息
     */
    @GetMapping("/plan/{planId}/stats")
    public AjaxResult getPlanStats(@PathVariable("planId") Long planId) {
        Map<String, Object> stats = planService.selectPlanStats(planId);
        return success(stats);
    }

    /**
     * 查询企业计划统计
     */
    @GetMapping("/enterprise/{enterpriseId}/stats")
    public AjaxResult getEnterpriseStats(@PathVariable("enterpriseId") Long enterpriseId) {
        Map<String, Object> stats = planService.selectEnterprisePlanStats(enterpriseId);
        return success(stats);
    }

    /**
     * 查询计划参与度分析
     */
    @GetMapping("/plan/{planId}/participation-analysis")
    public AjaxResult getParticipationAnalysis(@PathVariable("planId") Long planId) {
        Map<String, Object> analysis = planService.selectPlanParticipationAnalysis(planId);
        return success(analysis);
    }

    /**
     * 查询计划效果评估
     */
    @GetMapping("/plan/{planId}/effectiveness-analysis")
    public AjaxResult getEffectivenessAnalysis(@PathVariable("planId") Long planId) {
        Map<String, Object> analysis = planService.selectPlanEffectivenessAnalysis(planId);
        return success(analysis);
    }

    /**
     * 生成计划报告
     */
    @PostMapping("/plan/{planId}/report")
    public AjaxResult generatePlanReport(@PathVariable("planId") Long planId) {
        Map<String, Object> report = planService.generatePlanReport(planId);
        return success(report);
    }

    /**
     * 导出计划数据
     */
    @GetMapping("/plan/{planId}/export")
    public AjaxResult exportPlanData(@PathVariable("planId") Long planId) {
        Map<String, Object> data = planService.exportPlanData(planId);
        return success(data);
    }

    /**
     * 复制测评计划
     */
    @Log(title = "企业测评计划", businessType = BusinessType.INSERT)
    @PostMapping("/plan/{planId}/copy")
    public AjaxResult copyPlan(
            @PathVariable("planId") Long planId,
            @RequestBody PsyTEnterpriseAssessmentPlan targetPlan) {
        return toAjax(planService.copyAssessmentPlan(planId, targetPlan));
    }

    /**
     * 查询参与统计信息
     */
    @GetMapping("/plan/{planId}/participation-stats")
    public AjaxResult getParticipationStats(@PathVariable("planId") Long planId) {
        Map<String, Object> stats = participantService.selectParticipationStats(planId);
        return success(stats);
    }

    /**
     * 查询部门参与统计
     */
    @GetMapping("/plan/{planId}/department-stats")
    public AjaxResult getDepartmentStats(@PathVariable("planId") Long planId) {
        List<Map<String, Object>> stats = participantService.selectDepartmentParticipationStats(planId);
        return success(stats);
    }

    /**
     * 查询参与趋势统计
     */
    @GetMapping("/plan/{planId}/participation-trend")
    public AjaxResult getParticipationTrend(
            @PathVariable("planId") Long planId,
            @RequestParam(defaultValue = "30") Integer days) {
        List<Map<String, Object>> trend = participantService.selectParticipationTrendStats(planId, days);
        return success(trend);
    }

    /**
     * 查询参与度排行
     */
    @GetMapping("/enterprise/{enterpriseId}/participation-ranking")
    public AjaxResult getParticipationRanking(
            @PathVariable("enterpriseId") Long enterpriseId,
            @RequestParam(defaultValue = "10") Integer limit) {
        List<Map<String, Object>> ranking = participantService.selectParticipationRanking(enterpriseId, limit);
        return success(ranking);
    }

    /**
     * 查询企业测评使用统计
     */
    @GetMapping("/enterprise/{enterpriseId}/usage-stats")
    public AjaxResult getUsageStats(@PathVariable("enterpriseId") Long enterpriseId) {
        Map<String, Object> stats = enterpriseService.selectEnterpriseUsageStats(enterpriseId);
        return success(stats);
    }

    /**
     * 查询企业测评趋势
     */
    @GetMapping("/enterprise/{enterpriseId}/usage-trend")
    public AjaxResult getUsageTrend(
            @PathVariable("enterpriseId") Long enterpriseId,
            @RequestParam(defaultValue = "30") Integer days) {
        List<Map<String, Object>> trend = enterpriseService.selectEnterpriseUsageTrend(enterpriseId, days);
        return success(trend);
    }

    /**
     * 查询企业测评额度信息
     */
    @GetMapping("/enterprise/{enterpriseId}/quota")
    public AjaxResult getQuotaInfo(@PathVariable("enterpriseId") Long enterpriseId) {
        PsyTEnterprise enterprise = enterpriseService.selectEnterpriseById(enterpriseId);
        if (enterprise == null) {
            return error("企业信息不存在");
        }
        
        Map<String, Object> quotaInfo = Map.of(
            "assessmentQuota", enterprise.getAssessmentQuota(),
            "usedAssessmentCount", enterprise.getUsedAssessmentCount(),
            "remainingAssessmentCount", enterprise.getRemainingAssessmentCount(),
            "usageRate", enterprise.getUsedAssessmentCount() * 100.0 / enterprise.getAssessmentQuota()
        );
        
        return success(quotaInfo);
    }

    /**
     * 员工参与测评
     */
    @PostMapping("/employee/participate")
    public AjaxResult participateAssessment(
            @RequestParam Long planId,
            @RequestParam Long employeeId) {
        // 开始员工测评
        PsyTEnterpriseAssessmentPlan plan = planService.selectPlanById(planId);
        if (plan == null) {
            return error("测评计划不存在");
        }
        
        Map<String, Object> result = assessmentService.startAssessment(employeeId, plan.getScaleId(), "enterprise");
        return success(result);
    }

    /**
     * 查询员工参与历史
     */
    @GetMapping("/employee/{employeeId}/history")
    public TableDataInfo getEmployeeHistory(@PathVariable("employeeId") Long employeeId) {
        startPage();
        List<PsyTEnterpriseAssessmentParticipant> list = participantService.selectEmployeeParticipationHistory(employeeId);
        return getDataTable(list);
    }
}
